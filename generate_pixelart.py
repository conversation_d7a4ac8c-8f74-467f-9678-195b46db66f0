# generate_pixelart.py
import torch
from diffusers import StableDiffusionPipeline, StableDiffusionXLPipeline
from diffusers import EulerAncestralDiscreteScheduler
import os
from PIL import Image

class PixelArtGenerator:
    def __init__(self, model_path="D:\opt\models\base\SD_PixelArt_SpriteSheet_Generator"):
        self.model_path = model_path
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"使用设备: {self.device}")
        
        # 加载模型
        self.load_model()
    
    def load_model(self):
        """加载模型"""
        try:
            # 尝试加载SDXL模型
            self.pipe = StableDiffusionXLPipeline.from_pretrained(
                self.model_path,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
                safety_checker=None,
                requires_safety_checker=False
            )
        except:
            # 如果失败，尝试加载标准SD模型
            self.pipe = StableDiffusionPipeline.from_pretrained(
                self.model_path,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
                safety_checker=None,
                requires_safety_checker=False
            )
        
        # 配置调度器
        self.pipe.scheduler = EulerAncestralDiscreteScheduler.from_config(
            self.pipe.scheduler.config
        )
        
        # 移动到设备
        self.pipe = self.pipe.to(self.device)
        
        # 优化性能（如果使用CUDA）
        if self.device == "cuda":
            self.pipe.enable_xformers_memory_efficient_attention()
    
    def generate_sprite(self, prompt, negative_prompt="", output_path="output.png", 
                       steps=20, guidance_scale=7.5, width=512, height=512):
        """
        生成像素艺术精灵
        
        参数:
        prompt: 正面提示词
        negative_prompt: 负面提示词
        output_path: 输出文件路径
        steps: 推理步数
        guidance_scale: 引导尺度
        width, height: 图像尺寸
        """
        
        # 生成图像
        with torch.autocast(self.device):
            result = self.pipe(
                prompt=prompt,
                negative_prompt=negative_prompt,
                num_inference_steps=steps,
                guidance_scale=guidance_scale,
                width=width,
                height=height,
                num_images_per_prompt=1
            )
        
        # 保存图像
        image = result.images[0]
        image.save(output_path)
        print(f"图像已保存到: {output_path}")
        return image
    
    def generate_multiple_views(self, character_description, output_dir="./output"):
        """
        生成多个角度的精灵视图
        
        参数:
        character_description: 角色描述
        output_dir: 输出目录
        """
        
        os.makedirs(output_dir, exist_ok=True)
        
        # 不同角度的提示词模板
        view_templates = {
            "front": "PixelartFSS, {description}, front view, facing camera, pixel art, sprite sheet, 32x32 pixels, video game style",
            "back": "PixelartRSS, {description}, back view, facing away, pixel art, sprite sheet, 32x32 pixels, video game style", 
            "left": "PixelartLSS, {description}, left side view, profile, pixel art, sprite sheet, 32x32 pixels, video game style",
            "right": "PixelartRSS, {description}, right side view, profile, pixel art, sprite sheet, 32x32 pixels, video game style"
        }
        
        negative_prompt = "blurry, low quality, 3d, realistic, photo, smudged, messy, deformed"
        
        generated_images = {}
        
        for view_name, template in view_templates.items():
            prompt = template.format(description=character_description)
            output_path = os.path.join(output_dir, f"{view_name}_view.png")
            
            print(f"生成 {view_name} 视图...")
            image = self.generate_sprite(
                prompt=prompt,
                negative_prompt=negative_prompt,
                output_path=output_path,
                width=256,  # 像素艺术通常使用较小尺寸
                height=256,
                steps=25,
                guidance_scale=8.0
            )
            
            generated_images[view_name] = image
        
        return generated_images

def main():
    # 初始化生成器
    generator = PixelArtGenerator()
    
    # 示例：生成士兵的不同角度
    soldier_description = "medieval soldier with sword and shield, wearing armor, game character"
    
    print("开始生成士兵精灵...")
    images = generator.generate_multiple_views(
        character_description=soldier_description,
        output_dir="./soldier_sprites"
    )
    
    print("生成完成！")
    
    # 显示生成的图像（可选）
    for view_name, image in images.items():
        image.show()
        break  # 只显示一张避免同时打开多个窗口

if __name__ == "__main__":
    main()