# download_model.py
from huggingface_hub import snapshot_download, login
import os

def download_pixelart_model():
    # 设置镜像（国内用户推荐）
    os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'
    
    # 登录Hugging Face（如果需要）
    # 首先在Hugging Face网站获取token：https://huggingface.co/settings/tokens
    # token = "你的huggingface_token"
    # login(token=token)
    
    # 模型仓库ID
    repo_id = "Onodofthenorth/SD_PixelArt_SpriteSheet_Generator"
    
    # 本地保存目录
    local_dir = "./sd_pixelart_model"
    
    print("开始下载模型...")
    
    # 下载模型
    snapshot_download(
        repo_id=repo_id,
        local_dir=local_dir,
        local_dir_use_symlinks=False,  # 避免符号链接
        resume_download=True,          # 支持断点续传
        # revision="main",             # 指定版本，如果需要
    )
    
    print(f"模型已下载到: {local_dir}")
    return local_dir

if __name__ == "__main__":
    download_pixelart_model()