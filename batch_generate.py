# batch_generate.py
from generate_pixelart import PixelArtGenerator
import os

def batch_generate_characters():
    generator = PixelArtGenerator()
    
    # 定义要生成的不同角色
    characters = {
        "knight": "knight in full plate armor with greatsword, heroic stance",
        "archer": "elf archer with bow and quiver, forest camouflage, agile",
        "mage": "wizard with staff and robe, magical energy, wise",
        "zombie": "zombie soldier, rotting flesh, undead, menacing"
    }
    
    # 定义不同的动作状态
    actions = {
        "idle": "standing idle, neutral pose",
        "attack": "attacking with weapon, dynamic action pose",
        "defend": "blocking with shield, defensive stance", 
        "die": "dying animation, falling down, dramatic"
    }
    
    negative_prompt = "blurry, low quality, 3d, realistic, photo, smudged"
    
    for char_name, char_desc in characters.items():
        for action_name, action_desc in actions.items():
            # 创建输出目录
            output_dir = f"./characters/{char_name}/{action_name}"
            os.makedirs(output_dir, exist_ok=True)
            
            # 组合描述
            full_description = f"{char_desc}, {action_desc}, pixel art, sprite sheet, game asset"
            
            print(f"生成 {char_name} 的 {action_name} 动作...")
            
            try:
                # 生成多个角度
                generator.generate_multiple_views(
                    character_description=full_description,
                    output_dir=output_dir
                )
            except Exception as e:
                print(f"生成 {char_name} {action_name} 时出错: {e}")
                continue

if __name__ == "__main__":
    batch_generate_characters()